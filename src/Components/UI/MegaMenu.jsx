'use client';
import React, { useEffect, useState,useRef } from 'react';
import Link from 'next/link';
import { ChevronRight,ChevronDown } from 'lucide-react';

export default function MegaMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const menuRef = useRef(null);


  // Load JSON data from /public
  useEffect(() => {
    const fetchData = async () => {
      const res = await fetch('/tradereply-categories.json');
      const data = await res.json();
      setCategories(data.categories);
    };
    fetchData();
  }, []);

   // Close on outside click
   useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
  
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  


  return (

    <div className="relative z-[9999]" ref={menuRef}>
       <button
        onClick={() => setIsOpen(!isOpen)}
        className="px-4 py-2 text-white flex items-center gap-2"
        style={{
          fontFamily: 'Gilroy-SemiBold',
          fontWeight: 400,
          fontSize: '18px',
          lineHeight: '26px',
          letterSpacing: '-0.1px',
          textAlign: 'center',
          color: '#FFFFFF',
        }}
      >
        Shop <ChevronDown size={18} />
      </button>

      {isOpen && (
        <div
          className="absolute mt-2 w-[1059px]  rounded-[15px] border bg-white flex"
          style={{
            border:"1px solid #FFFFFF4D",
            boxShadow: '0px 4px 10px 0px #FFFFFF26',
            borderRadius:'15px',
            zIndex: 9999
          }}
        >
          {/* Left panel: level1 categories */}
          <div className="w-[300px] bg-[#F5F5F5] p-4 space-y-2" 
          style={{borderRight:'1px solid #D9D9D9'}}
          >
            {categories.map((cat) => (
              <div
                key={cat.slug}
                onMouseEnter={() => setActiveCategory(cat)}
                className={`cursor-pointer flex items-center justify-between mx-2 p-2 my-4 rounded-md text-[16px] font-semibold transition ${activeCategory?.slug === cat.slug
                  ? 'bg-white text-black shadow-sm'
                  : 'text-black hover:bg-[#e6e6e6]'
                  }`}
              >
                <span style={{
                  fontFamily: 'Gilroy',
                  fontWeight: 600,
                  fontSize: '20px',
                  lineHeight: '100%',
                  letterSpacing: '-0.1px',
                  // height: '40px',
                  // width: '173px',
                }}  >{cat.name}</span>
                <ChevronRight className="w-4 h-4" />
              </div>
            ))}
          </div>

          {/* Right panel: level2 + level3 */}
          {activeCategory && (
            <div className=" p-6 bg-white overflow-x-auto "
            style={{borderTopRightRadius:"15px",borderBottomRightRadius:'15px', width:'750px'}}
            >
              <div className="flex flex-row gap-x-12">
                {activeCategory.subcategories.map((sub) => (
                  <div key={sub.slug} className="min-w-[300px] p-4 space-y-2">
                    {/* <h4 className="text-[15px] px-4 py-2 font-bold text-black mb-3">{sub.name}</h4> */}
                    <p className="text-black  capitalize"
                      style={{
                        fontFamily: 'Gilroy',
                        fontWeight: 700,
                        fontSize: '16px',
                        lineHeight: '100%',
                        letterSpacing: '-0.1px',
                        height: '40px',
                        width: '173px',
                      }}
                    >
                      {sub.name}
                    </p>
                    <ul className="space-y-1">
                      {sub.subcategories.map((item) => (
                        <li key={item.slug}>
                          <Link
                            href={`/marketplace/categories/${activeCategory.slug}/${sub.slug}/${item.slug}`}
                            className="py-2 hover:underline block"
                            style={{
                              fontFamily: 'Gilroy',
                              fontWeight: 500,
                              fontSize: '16px',
                              lineHeight: '100%',
                              letterSpacing: '-0.1px',
                              color: '#000000CC'

                            }}
                          >
                            {item.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>

  );

}
